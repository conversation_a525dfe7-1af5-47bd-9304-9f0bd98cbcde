<!DOCTYPE html>
<html>
<head>
    <title>Sentinel Image Analysis</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .image-upload {
            background: white;
            margin: 20px 0;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-upload h2 {
            color: #34495e;
            margin-bottom: 20px;
        }
        
        .file-input-container {
            margin: 15px 0;
            padding: 15px;
            border: 2px dashed #3498db;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .file-input-container:hover {
            border-color: #2980b9;
            background: #f7f9fc;
        }
        
        input[type="file"] {
            display: block;
            margin: 10px 0;
            padding: 8px;
            width: 100%;
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .results {
            margin-top: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .metric-card p {
            font-size: 24px;
            color: #3498db;
            margin: 0;
        }
        
        .image-display {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 30px;
        }
        
        .image-container {
            text-align: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .image-container h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .image-container img:hover {
            transform: scale(1.05);
        }
        
        #errorMessage {
            color: #e74c3c;
            background: #fdf0ed;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
            text-align: center;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            color: #2c3e50;
        }
        
        .performance-plot {
            margin-top: 30px;
            text-align: center;
        }
        
        .performance-plot img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>Sentinel Image Analysis</h1>
    
    <div class="image-upload">
        <h2>Upload Images</h2>
        <div>
            <label for="s1Image">Sentinel-1 Image:</label>
            <input type="file" id="s1Image" accept="image/*" required>
        </div>
        <div>
            <label for="s2Image">Sentinel-2 Image:</label>
            <input type="file" id="s2Image" accept="image/*" required>
        </div>
        <button onclick="processImages()">Analyze Images</button>
    </div>

    <div id="errorMessage"></div>
    
    <div class="loading" id="loadingIndicator">
        Processing images... Please wait.
    </div>

    <div class="results" id="results">
        <div class="metrics">
            <div class="metric-card">
                <h3>Burnt Area</h3>
                <p id="areaValue">-- km²</p>
            </div>
            <div class="metric-card">
                <h3>Accuracy</h3>
                <p id="accuracyValue">--%</p>
            </div>
            <div class="metric-card">
                <h3>F1-Score</h3>
                <p id="f1Value">--</p>
            </div>
        </div>
        
        <div class="image-display">
            <div class="image-container">
                <h3>Sentinel-1 Image</h3>
                <img id="s1Preview" src="" alt="Sentinel-1">
            </div>
            <div class="image-container">
                <h3>Sentinel-2 Image</h3>
                <img id="s2Preview" src="" alt="Sentinel-2">
            </div>
            <div class="image-container">
                <h3>Generated Mask</h3>
                <img id="maskImage" src="" alt="Generated Mask">
            </div>
        </div>
        
        <div class="performance-plot">
            <h3>Model Performance</h3>
            <img id="plotImage" src="" alt="Performance Plot">
        </div>
    </div>

    <script>
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function clearError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
        }

        function updateResults(data) {
            document.getElementById('areaValue').textContent = `${data.area} km²`;
            document.getElementById('accuracyValue').textContent = `${data.accuracy}%`;
            document.getElementById('f1Value').textContent = data.f1_score;
            document.getElementById('plotImage').src = `data:image/png;base64,${data.plot}`;
            if (data.mask) {
                document.getElementById('maskImage').src = `data:image/png;base64,${data.mask}`;
            }
            document.getElementById('results').style.display = 'block';
        }

        function previewImage(file, previewId) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById(previewId).src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        async function processImages() {
            clearError();
            const s1File = document.getElementById('s1Image').files[0];
            const s2File = document.getElementById('s2Image').files[0];
            
            if (!s1File || !s2File) {
                showError('Please select both Sentinel-1 and Sentinel-2 images.');
                return;
            }

            // Preview images
            previewImage(s1File, 's1Preview');
            previewImage(s2File, 's2Preview');

            const formData = new FormData();
            formData.append('s1_image', s1File);
            formData.append('s2_image', s2File);

            showLoading(true);

            try {
                const response = await fetch('/process', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                showLoading(false);

                if (data.error) {
                    showError(data.error);
                    return;
                }

                updateResults(data);
            } catch (error) {
                showLoading(false);
                showError('An error occurred while processing the images.');
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>