"""
CPU-Optimized Training Script for Maximum Test Accuracy
Designed specifically for CPU training with comprehensive evaluation
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from pathlib import Path
import cv2
import numpy as np
from tqdm import tqdm
import os
import time
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, confusion_matrix
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
try:
    import seaborn as sns
except ImportError:
    print("Seaborn not available, using matplotlib only")
    sns = None

# For real-time plotting
plt.ion()  # Enable interactive mode

# Import our modules
from darunet_cpu_optimized import CPUOptimizedDARUNet, CombinedLoss
from data_preprocessing import DataPreprocessor

class OptimizedDataset(Dataset):
    """Optimized dataset for CPU training"""
    
    def __init__(self, s1_paths, s2_paths, mask_paths, use_all_s2_channels=True, augment=False):
        self.s1_paths = s1_paths
        self.s2_paths = s2_paths
        self.mask_paths = mask_paths
        self.preprocessor = DataPreprocessor(use_paper_config=not use_all_s2_channels)
        self.augment = augment
        
    def __len__(self):
        return len(self.s1_paths)
    
    def __getitem__(self, idx):
        try:
            # Load images
            s1_img = cv2.imread(str(self.s1_paths[idx]), cv2.IMREAD_GRAYSCALE)
            s2_img = cv2.imread(str(self.s2_paths[idx]))
            mask = cv2.imread(str(self.mask_paths[idx]), cv2.IMREAD_GRAYSCALE)
            
            if s1_img is None or s2_img is None or mask is None:
                raise ValueError("Could not load images")
            
            # Preprocess
            s1_processed = self.preprocessor.preprocess_sentinel1(s1_img)
            s2_processed = self.preprocessor.preprocess_sentinel2(s2_img)
            
            # Data augmentation for training (fix negative stride issue)
            if self.augment and np.random.random() > 0.5:
                # Random horizontal flip
                if np.random.random() > 0.5:
                    s1_processed = np.fliplr(s1_processed).copy()
                    s2_processed = np.fliplr(s2_processed).copy()
                    mask = np.fliplr(mask).copy()

                # Random vertical flip
                if np.random.random() > 0.5:
                    s1_processed = np.flipud(s1_processed).copy()
                    s2_processed = np.flipud(s2_processed).copy()
                    mask = np.flipud(mask).copy()
            
            # Convert to tensors
            s1_tensor = torch.from_numpy(s1_processed).float().unsqueeze(0)
            s2_tensor = torch.from_numpy(s2_processed).float().permute(2, 0, 1)
            
            # Process mask
            mask = (mask > 127).astype(np.int64)  # Use np.int64 instead of deprecated np.long
            mask_tensor = torch.from_numpy(mask).long()
            
            return s1_tensor, s2_tensor, mask_tensor
            
        except Exception as e:
            print(f"Error processing sample {idx}: {str(e)}")
            # Return dummy data
            s1_tensor = torch.zeros((1, 256, 256), dtype=torch.float32)
            s2_tensor = torch.zeros((12, 256, 256), dtype=torch.float32)
            mask_tensor = torch.zeros((256, 256), dtype=torch.long)
            return s1_tensor, s2_tensor, mask_tensor

class CPUOptimizedTrainer:
    """CPU-Optimized trainer for maximum accuracy"""

    def __init__(self, model, learning_rate=1e-4):
        # Force CPU usage
        self.device = torch.device('cpu')
        self.model = model.to(self.device)

        # Ensure model is in CPU mode
        print(f"Model device: {next(model.parameters()).device}")
        print(f"Training device: {self.device}")

        # Use combined loss for better performance
        self.criterion = CombinedLoss(focal_weight=0.7, dice_weight=0.3)

        # Optimizer optimized for CPU
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=learning_rate,
            weight_decay=1e-3,
            amsgrad=True
        )

        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.5, patience=10
        )

        # Metrics tracking
        self.train_losses = []
        self.val_losses = []
        self.val_accuracies = []
        self.val_f1_scores = []
        self.val_precisions = []  # Added precision tracking
        self.val_recalls = []     # Added recall tracking
        self.best_f1 = 0.0
        self.best_accuracy = 0.0
        
    def calculate_metrics(self, outputs, targets):
        """Calculate comprehensive metrics"""
        with torch.no_grad():
            # Get predictions
            _, predictions = torch.max(outputs, dim=1)
            
            # Flatten for sklearn metrics
            targets_flat = targets.cpu().numpy().flatten()
            predictions_flat = predictions.cpu().numpy().flatten()
            
            # Calculate metrics
            accuracy = accuracy_score(targets_flat, predictions_flat) * 100
            f1 = f1_score(targets_flat, predictions_flat, average='weighted', zero_division=0)
            precision = precision_score(targets_flat, predictions_flat, average='weighted', zero_division=0)
            recall = recall_score(targets_flat, predictions_flat, average='weighted', zero_division=0)
            
            return accuracy, f1, precision, recall
    
    def train_epoch(self, train_loader):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        total_accuracy = 0.0
        total_f1 = 0.0
        
        progress_bar = tqdm(train_loader, desc="Training", leave=False)
        
        for batch_idx, (s1_imgs, s2_imgs, masks) in enumerate(progress_bar):
            # Ensure all tensors are on CPU
            s1_imgs = s1_imgs.to(self.device)
            s2_imgs = s2_imgs.to(self.device)
            masks = masks.to(self.device)

            # Forward pass
            outputs = self.model(s1_imgs, s2_imgs)
            loss = self.criterion(outputs, masks)
            
            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Calculate metrics
            accuracy, f1, _, _ = self.calculate_metrics(outputs, masks)
            
            # Update totals
            total_loss += loss.item()
            total_accuracy += accuracy
            total_f1 += f1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{accuracy:.2f}%',
                'f1': f'{f1:.4f}'
            })
        
        return total_loss / len(train_loader), total_accuracy / len(train_loader), total_f1 / len(train_loader)
    
    def validate(self, val_loader):
        """Validate the model"""
        self.model.eval()
        total_loss = 0.0
        total_accuracy = 0.0
        total_f1 = 0.0
        total_precision = 0.0
        total_recall = 0.0

        with torch.no_grad():
            for s1_imgs, s2_imgs, masks in tqdm(val_loader, desc="Validation", leave=False):
                # Ensure all tensors are on CPU
                s1_imgs = s1_imgs.to(self.device)
                s2_imgs = s2_imgs.to(self.device)
                masks = masks.to(self.device)

                outputs = self.model(s1_imgs, s2_imgs)
                loss = self.criterion(outputs, masks)

                accuracy, f1, precision, recall = self.calculate_metrics(outputs, masks)

                total_loss += loss.item()
                total_accuracy += accuracy
                total_f1 += f1
                total_precision += precision
                total_recall += recall

        return (total_loss / len(val_loader),
                total_accuracy / len(val_loader),
                total_f1 / len(val_loader),
                total_precision / len(val_loader),
                total_recall / len(val_loader))
    
    def test(self, test_loader):
        """Comprehensive test evaluation"""
        self.model.eval()
        all_predictions = []
        all_targets = []
        test_loss = 0.0
        
        print("Running comprehensive test evaluation...")
        
        with torch.no_grad():
            for s1_imgs, s2_imgs, masks in tqdm(test_loader, desc="Testing"):
                # Ensure all tensors are on CPU
                s1_imgs = s1_imgs.to(self.device)
                s2_imgs = s2_imgs.to(self.device)
                masks = masks.to(self.device)

                outputs = self.model(s1_imgs, s2_imgs)
                loss = self.criterion(outputs, masks)
                
                _, predictions = torch.max(outputs, dim=1)
                
                all_predictions.extend(predictions.cpu().numpy().flatten())
                all_targets.extend(masks.cpu().numpy().flatten())
                test_loss += loss.item()
        
        # Calculate comprehensive metrics
        test_accuracy = accuracy_score(all_targets, all_predictions) * 100
        test_f1 = f1_score(all_targets, all_predictions, average='weighted', zero_division=0)
        test_precision = precision_score(all_targets, all_predictions, average='weighted', zero_division=0)
        test_recall = recall_score(all_targets, all_predictions, average='weighted', zero_division=0)
        
        # Confusion matrix
        cm = confusion_matrix(all_targets, all_predictions)
        
        return {
            'loss': test_loss / len(test_loader),
            'accuracy': test_accuracy,
            'f1_score': test_f1,
            'precision': test_precision,
            'recall': test_recall,
            'confusion_matrix': cm
        }

def plot_real_time_progress(train_losses, val_losses, val_accuracies, val_f1_scores, val_precisions, val_recalls, epoch, save_dir='results'):
    """Plot real-time training progress"""
    if len(train_losses) < 2:
        return

    # Create directory for plots
    os.makedirs(save_dir, exist_ok=True)

    # Figure 1: Combined plots (2x2 grid)
    plt.figure(figsize=(15, 10))

    # Plot 1: Loss curves
    plt.subplot(2, 2, 1)
    epochs = range(1, len(train_losses) + 1)
    plt.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
    plt.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot 2: Validation Accuracy
    plt.subplot(2, 2, 2)
    plt.plot(epochs, val_accuracies, 'g-', label='Validation Accuracy', linewidth=2)
    plt.title('Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot 3: Validation F1 Score
    plt.subplot(2, 2, 3)
    plt.plot(epochs, val_f1_scores, 'm-', label='Validation F1 Score', linewidth=2)
    plt.title('Validation F1 Score')
    plt.xlabel('Epoch')
    plt.ylabel('F1 Score')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Plot 4: Combined metrics
    plt.subplot(2, 2, 4)
    # Normalize accuracy to 0-1 scale for comparison
    normalized_acc = [acc/100 for acc in val_accuracies]
    plt.plot(epochs, normalized_acc, 'g-', label='Val Accuracy (normalized)', linewidth=2)
    plt.plot(epochs, val_f1_scores, 'm-', label='Val F1 Score', linewidth=2)
    plt.title('Combined Validation Metrics')
    plt.xlabel('Epoch')
    plt.ylabel('Score')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save the combined plot
    plt.savefig(f'{save_dir}/training_progress_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
    plt.savefig(f'{save_dir}/training_progress_latest.png', dpi=150, bbox_inches='tight')
    plt.close()

    # Figure 2: Line graph for accuracy during training
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, val_accuracies, 'g-', label='Validation Accuracy', linewidth=3, marker='o', markersize=5)
    plt.title('Validation Accuracy During Training')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/accuracy_line_graph_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
    plt.savefig(f'{save_dir}/accuracy_line_graph_latest.png', dpi=150, bbox_inches='tight')
    plt.close()

    # Figure 2b: Line graph for F1-Score during training
    plt.figure(figsize=(10, 6))
    plt.plot(epochs, val_f1_scores, 'm-', label='Validation F1-Score', linewidth=3, marker='o', markersize=5)
    plt.title('Validation F1-Score During Training')
    plt.xlabel('Epoch')
    plt.ylabel('F1-Score')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{save_dir}/f1_score_line_graph_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
    plt.savefig(f'{save_dir}/f1_score_line_graph_latest.png', dpi=150, bbox_inches='tight')
    plt.close()

    # Figure 2c: Combined line graph for Accuracy and F1-Score
    plt.figure(figsize=(12, 6))

    # Create dual y-axis
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # Plot accuracy on left y-axis
    color = 'tab:green'
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy (%)', color=color)
    line1 = ax1.plot(epochs, val_accuracies, 'g-', label='Validation Accuracy', linewidth=3, marker='o', markersize=5, color=color)
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)

    # Create second y-axis for F1-score
    ax2 = ax1.twinx()
    color = 'tab:purple'
    ax2.set_ylabel('F1-Score', color=color)
    line2 = ax2.plot(epochs, val_f1_scores, 'm-', label='Validation F1-Score', linewidth=3, marker='s', markersize=5, color=color)
    ax2.tick_params(axis='y', labelcolor=color)

    # Add title and legend
    plt.title('Validation Accuracy and F1-Score During Training')

    # Combine legends
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')

    plt.tight_layout()
    plt.savefig(f'{save_dir}/accuracy_f1_combined_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
    plt.savefig(f'{save_dir}/accuracy_f1_combined_latest.png', dpi=150, bbox_inches='tight')
    plt.close()

    # Figure 3: Bar graph for latest accuracy and precision
    # Only create this after a few epochs when we have meaningful data
    if len(val_accuracies) >= 3 and len(val_precisions) >= 3:
        plt.figure(figsize=(12, 6))

        # Get the latest metrics
        latest_accuracy = val_accuracies[-1]
        latest_precision = val_precisions[-1] * 100  # Convert to percentage
        latest_recall = val_recalls[-1] * 100  # Convert to percentage
        latest_f1 = val_f1_scores[-1] * 100  # Convert to percentage

        metrics = ['Accuracy', 'Precision', 'Recall', 'F1 Score']
        values = [latest_accuracy, latest_precision, latest_recall, latest_f1]
        colors = ['green', 'blue', 'orange', 'purple']

        # Create bar chart
        bars = plt.bar(metrics, values, color=colors, width=0.6)

        # Add value labels on top of bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.2f}%', ha='center', va='bottom', fontweight='bold')

        plt.title(f'Performance Metrics (Epoch {epoch})')
        plt.ylabel('Value (%)')
        plt.ylim(0, max(values) * 1.15)  # Add some headroom for labels
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()

        plt.savefig(f'{save_dir}/metrics_bar_graph_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
        plt.savefig(f'{save_dir}/metrics_bar_graph_latest.png', dpi=150, bbox_inches='tight')
        plt.close()

        # Figure 4: Separate bar graph for Accuracy and Precision comparison
        plt.figure(figsize=(8, 6))

        acc_prec_metrics = ['Accuracy', 'Precision']
        acc_prec_values = [latest_accuracy, latest_precision]
        acc_prec_colors = ['green', 'blue']

        bars = plt.bar(acc_prec_metrics, acc_prec_values, color=acc_prec_colors, width=0.5)

        # Add value labels on top of bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.2f}%', ha='center', va='bottom', fontweight='bold', fontsize=12)

        plt.title(f'Accuracy vs Precision (Epoch {epoch})')
        plt.ylabel('Value (%)')
        plt.ylim(0, max(acc_prec_values) * 1.15)
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()

        plt.savefig(f'{save_dir}/accuracy_precision_bar_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
        plt.savefig(f'{save_dir}/accuracy_precision_bar_latest.png', dpi=150, bbox_inches='tight')
        plt.close()

        # Figure 5: F1-Score bar graph
        plt.figure(figsize=(6, 6))

        # Create a single bar for F1-Score
        f1_bar = plt.bar(['F1-Score'], [latest_f1], color='purple', width=0.4)

        # Add value label
        for bar in f1_bar:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.2f}%', ha='center', va='bottom', fontweight='bold', fontsize=14)

        plt.title(f'F1-Score (Epoch {epoch})')
        plt.ylabel('Value (%)')
        plt.ylim(0, latest_f1 * 1.2)  # Add 20% headroom
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()

        plt.savefig(f'{save_dir}/f1_score_bar_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
        plt.savefig(f'{save_dir}/f1_score_bar_latest.png', dpi=150, bbox_inches='tight')
        plt.close()

    # Show the plot briefly
    plt.pause(0.1)

def plot_confusion_matrix(cm, save_path):
    """Plot and save confusion matrix"""
    plt.figure(figsize=(8, 6))

    if sns is not None:
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=['Non-Burned', 'Burned'],
                    yticklabels=['Non-Burned', 'Burned'])
    else:
        # Fallback to matplotlib only
        plt.imshow(cm, interpolation='nearest', cmap='Blues')
        plt.colorbar()

        # Add text annotations
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                plt.text(j, i, str(cm[i, j]), ha='center', va='center')

        plt.xticks([0, 1], ['Non-Burned', 'Burned'])
        plt.yticks([0, 1], ['Non-Burned', 'Burned'])

    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main training function optimized for CPU"""
    # Force CPU usage - disable CUDA completely
    torch.cuda.is_available = lambda: False
    torch.backends.cudnn.enabled = False

    # Configuration for maximum test accuracy on CPU
    use_all_s2_channels = True  # Use all 12 channels for better accuracy
    num_epochs = 50  # Set to 50 epochs as requested
    batch_size = 1  # Optimal for CPU
    learning_rate = 1e-4

    print("CPU-ONLY DARU-Net Training for Maximum Test Accuracy")
    print("=" * 60)
    print("GPU DISABLED - Training on CPU only")
    print(f"Device: {torch.device('cpu')}")
    
    # Create results directory
    os.makedirs('results', exist_ok=True)
    
    # Data paths
    s1_dir = Path('data/sentinel1')
    s2_dir = Path('data/sentinel2')
    mask_dir = Path('data/masks')
    
    # Get file paths
    s1_paths = sorted(list(s1_dir.glob('*.png')))
    valid_triplets = []
    for s1_path in s1_paths:
        s2_path = s2_dir / s1_path.name.replace('_s1_', '_s2_')
        mask_path = mask_dir / s1_path.name
        
        if s2_path.exists() and mask_path.exists():
            valid_triplets.append((s1_path, s2_path, mask_path))
    
    s1_paths, s2_paths, mask_paths = zip(*valid_triplets) if valid_triplets else ([], [], [])
    
    print(f"Found {len(s1_paths)} valid image triplets")
    
    # Split data: 50 validation, 50 test, remaining for training
    total_samples = len(s1_paths)

    # Ensure we have enough samples
    if total_samples < 100:
        print(f"Warning: Only {total_samples} samples available. Need at least 100 for 50 val + 50 test.")
        # Adjust to use available samples
        val_size = min(50, total_samples // 3)
        test_size = min(50, total_samples // 3)
    else:
        val_size = 50
        test_size = 50

    # First split: separate validation set (50 samples)
    s1_temp, s1_val, s2_temp, s2_val, mask_temp, mask_val = train_test_split(
        s1_paths, s2_paths, mask_paths, test_size=val_size, random_state=42
    )

    # Second split: separate test set (50 samples) from remaining
    s1_train, s1_test, s2_train, s2_test, mask_train, mask_test = train_test_split(
        s1_temp, s2_temp, mask_temp, test_size=test_size, random_state=42
    )

    print(f"Training samples: {len(s1_train)} ({len(s1_train)/total_samples*100:.1f}%)")
    print(f"Validation samples: {len(s1_val)} ({len(s1_val)/total_samples*100:.1f}%)")
    print(f"Test samples: {len(s1_test)} ({len(s1_test)/total_samples*100:.1f}%)")
    
    # Create datasets
    train_dataset = OptimizedDataset(s1_train, s2_train, mask_train, use_all_s2_channels, augment=True)
    val_dataset = OptimizedDataset(s1_val, s2_val, mask_val, use_all_s2_channels, augment=False)
    test_dataset = OptimizedDataset(s1_test, s2_test, mask_test, use_all_s2_channels, augment=False)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
    
    # Initialize model
    model = CPUOptimizedDARUNet(use_all_s2_channels=use_all_s2_channels)
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Initialize trainer
    trainer = CPUOptimizedTrainer(model, learning_rate=learning_rate)
    
    print(f"\nStarting training for {num_epochs} epochs...")
    print("Configuration: CPU-Optimized, All S2 channels, Combined Loss")
    
    # Training loop
    start_time = time.time()
    patience = 20
    no_improve_count = 0
    
    for epoch in range(num_epochs):
        epoch_start = time.time()
        
        # Train
        train_loss, train_acc, train_f1 = trainer.train_epoch(train_loader)
        
        # Validate
        val_loss, val_acc, val_f1, val_precision, val_recall = trainer.validate(val_loader)

        # Update scheduler based on validation accuracy
        trainer.scheduler.step(val_acc)

        # Store metrics
        trainer.train_losses.append(train_loss)
        trainer.val_losses.append(val_loss)
        trainer.val_accuracies.append(val_acc)
        trainer.val_f1_scores.append(val_f1)
        trainer.val_precisions.append(val_precision)
        trainer.val_recalls.append(val_recall)

        # Print progress
        epoch_time = time.time() - epoch_start
        print(f"\nEpoch {epoch+1}/{num_epochs} ({epoch_time:.2f}s)")
        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, Train F1: {train_f1:.4f}")
        print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Val F1: {val_f1:.4f}")
        print(f"Val Precision: {val_precision:.4f}, Val Recall: {val_recall:.4f}")

        # Plot real-time progress
        plot_real_time_progress(
            trainer.train_losses,
            trainer.val_losses,
            trainer.val_accuracies,
            trainer.val_f1_scores,
            trainer.val_precisions,
            trainer.val_recalls,
            epoch+1
        )

        # Save best model based on validation accuracy
        if val_acc > trainer.best_accuracy:
            trainer.best_accuracy = val_acc
            trainer.best_f1 = val_f1
            no_improve_count = 0

            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': trainer.optimizer.state_dict(),
                'val_f1': val_f1,
                'val_accuracy': val_acc,
                'config': {
                    'use_all_s2_channels': use_all_s2_channels,
                    'num_epochs': num_epochs,
                    'batch_size': batch_size,
                    'learning_rate': learning_rate
                }
            }, 'results/best_cpu_optimized_model.pth')
            print(f"Saved new best model with Accuracy: {val_acc:.2f}%, F1: {val_f1:.4f}")
        else:
            no_improve_count += 1
        
        # Early stopping
        if no_improve_count >= patience:
            print(f"Early stopping triggered after {epoch+1} epochs")
            break
        
        # Save checkpoint every 20 epochs
        if (epoch + 1) % 20 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': trainer.optimizer.state_dict(),
                'metrics': {
                    'train_losses': trainer.train_losses,
                    'val_losses': trainer.val_losses,
                    'val_accuracies': trainer.val_accuracies,
                    'val_f1_scores': trainer.val_f1_scores,
                    'val_precisions': trainer.val_precisions,
                    'val_recalls': trainer.val_recalls
                }
            }, f'results/checkpoint_epoch_{epoch+1}.pth')
    
    # Load best model for testing
    print("\nLoading best model for final test evaluation...")
    checkpoint = torch.load('results/best_cpu_optimized_model.pth')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Comprehensive test evaluation
    test_results = trainer.test(test_loader)
    
    # Print final results
    total_time = time.time() - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    print(f"Training completed in {int(hours)}h {int(minutes)}m {int(seconds)}s")
    print(f"Best validation F1 score: {trainer.best_f1:.4f}")
    print(f"Best validation accuracy: {trainer.best_accuracy:.2f}%")
    print(f"\nTEST SET RESULTS:")
    print(f"Test Accuracy: {test_results['accuracy']:.2f}%")
    print(f"Test F1 Score: {test_results['f1_score']:.4f}")
    print(f"Test Precision: {test_results['precision']:.4f}")
    print(f"Test Recall: {test_results['recall']:.4f}")
    print(f"Test Loss: {test_results['loss']:.4f}")
    
    # Save test results
    with open('results/test_results.json', 'w') as f:
        json.dump({
            'test_accuracy': test_results['accuracy'],
            'test_f1_score': test_results['f1_score'],
            'test_precision': test_results['precision'],
            'test_recall': test_results['recall'],
            'test_loss': test_results['loss'],
            'best_val_f1': trainer.best_f1,
            'best_val_accuracy': trainer.best_accuracy,
            'training_time_hours': total_time / 3600,
            'model_parameters': sum(p.numel() for p in model.parameters())
        }, f, indent=2)
    
    # Plot confusion matrix
    plot_confusion_matrix(test_results['confusion_matrix'], 'results/confusion_matrix.png')

    # Plot final training curves with enhanced visualization
    plot_real_time_progress(
        trainer.train_losses,
        trainer.val_losses,
        trainer.val_accuracies,
        trainer.val_f1_scores,
        trainer.val_precisions,
        trainer.val_recalls,
        len(trainer.train_losses),
        save_dir='results'
    )

    # Also create a simple version for compatibility
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.plot(trainer.train_losses, label='Train Loss', linewidth=2)
    plt.plot(trainer.val_losses, label='Val Loss', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Loss Curves')
    plt.grid(True, alpha=0.3)

    plt.subplot(1, 3, 2)
    plt.plot(trainer.val_accuracies, label='Val Accuracy', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.title('Validation Accuracy')
    plt.grid(True, alpha=0.3)

    plt.subplot(1, 3, 3)
    plt.plot(trainer.val_f1_scores, label='Val F1 Score', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('F1 Score')
    plt.legend()
    plt.title('Validation F1 Score')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/training_curves_final.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"\nResults saved to 'results/' directory")
    print(f"Confusion matrix saved to 'results/confusion_matrix.png'")
    print(f"Training curves saved to 'results/training_curves.png'")

if __name__ == '__main__':
    main()
